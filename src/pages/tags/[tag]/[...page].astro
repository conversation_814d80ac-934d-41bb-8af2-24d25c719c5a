---
import { render } from "astro:content";
import Pagination from "@/components/Paginator.astro";
import PostPreview from "@/components/blog/PostPreview.astro";
import { getAllPosts, getTagMeta, getUniqueTags } from "@/data/post";
import PageLayout from "@/layouts/Base.astro";
import { collectionDateSort } from "@/utils/date";
import type { GetStaticPaths, InferGetStaticPropsType } from "astro";
import { Icon } from "astro-icon/components";

export const getStaticPaths = (async ({ paginate }) => {
	const allPosts = await getAllPosts();
	const sortedPosts = allPosts.sort(collectionDateSort);
	const uniqueTags = getUniqueTags(sortedPosts);

	return uniqueTags.flatMap((tag) => {
		const postsWithTag = sortedPosts.filter((post) =>
			post.data.tags.includes(tag),
		);
		return paginate(postsWithTag, {
			pageSize: 10,
			params: { tag },
		});
	});
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { page } = Astro.props as Props;
const { tag } = Astro.params;
const tagMeta = await getTagMeta(tag);

const TagContent = tagMeta ? (await render(tagMeta)).Content : null;

const meta = {
	description:
		tagMeta?.data.description ?? `View all posts with the tag - ${tag}`,
	title: tagMeta?.data.title ?? `Posts about ${tag}`,
};

const paginationProps = {
	...(page.url.prev && {
		prevUrl: {
			text: "← Previous Tags",
			url: page.url.prev,
		},
	}),
	...(page.url.next && {
		nextUrl: {
			text: "Next Tags →",
			url: page.url.next,
		},
	}),
};
---

<PageLayout meta={meta}>
	<nav class="mb-8" aria-label="Breadcrumbs">
		<ul class="flex items-center">
			<li class="flex items-center">
				<a class="text-accent" href="/tags/">Tags</a>
				<Icon
					aria-hidden="true"
					name="mdi:chevron-right"
					class="mx-1.5"
				/>
			</li>
			<li aria-current="page" class="">
				<span aria-hidden="true">#</span>{tag}
			</li>
		</ul>
	</nav>
	<h1 class="title capitalize">
		{tagMeta?.data.title ?? `Posts about ${tag}`}
	</h1>
	<div class="prose prose-lg prose-cactus mb-16 max-w-none">
		{tagMeta?.data.description && <p>{tagMeta.data.description}</p>}
		{TagContent && <TagContent />}
	</div>
	<ul class="space-y-4">
		{
			page.data.map((p) => (
				<li class="gap-1 sm:grid-cols-[auto_1fr] grid">
					<PostPreview as="h2" post={p} />
				</li>
			))
		}
	</ul>
	<Pagination {...paginationProps} />
</PageLayout>
